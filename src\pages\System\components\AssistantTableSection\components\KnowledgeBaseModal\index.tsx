import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Tooltip, message, Popover, Checkbox } from 'antd';
import { SearchOutlined, UploadOutlined, EyeOutlined, DownloadOutlined, MoreOutlined, DeleteOutlined } from '@ant-design/icons';
import { downloadFile } from '@/services/DataLoom/fileController';
import { updateAiRole, deleteAiRoleFile } from '@/services/DataLoom/assistantController';
import './index.less';

const { Search } = Input;

interface KnowledgeBaseModalProps {
  visible: boolean;
  assistantData?: any;
  onCancel: () => void;
  onRefresh?: () => void;
}

interface FileItem {
  id: string; // 唯一标识符
  name: string;
  type: 'pdf' | 'doc' | 'docx' | 'txt';
  size?: string;
  uploadTime?: string;
  uploading?: boolean; // 标记是否正在上传
}

const KnowledgeBaseModal: React.FC<KnowledgeBaseModalProps> = ({ visible, assistantData, onCancel, onRefresh }) => {
  const [searchValue, setSearchValue] = useState('');
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]); // 选中的文件名列表

  // 解析docFiles字段并管理选中状态
  useEffect(() => {
    if (visible && assistantData?.docFiles) {
      try {
        const files = JSON.parse(assistantData.docFiles);
        const parsedFiles: FileItem[] = files.map((fileName: string, index: number) => {
          const extension = fileName.split('.').pop()?.toLowerCase();
          let type: 'pdf' | 'doc' | 'docx' | 'txt' = 'pdf';

          if (extension === 'doc' || extension === 'docx') {
            type = extension as 'doc' | 'docx';
          } else if (extension === 'txt') {
            type = 'txt';
          }

          return {
            id: `${fileName}-${index}`, // 使用文件名+索引作为唯一ID
            name: fileName,
            type,
            size: '2.5MB', // 模拟文件大小
            uploadTime: new Date().toLocaleString(), // 使用当前时间
          };
        });
        setFileList(parsedFiles);
      } catch (error) {
        console.error('解析docFiles失败:', error);
        setFileList([]);
      }
    } else {
      setFileList([]);
    }

    // 弹窗关闭时清空选中状态
    if (!visible) {
      setSelectedFiles([]);
    }
  }, [visible, assistantData?.docFiles]);

  // 获取文件图标
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return '/assets/image_1754034750309_9xzf6f.svg';
      case 'doc':
      case 'docx':
        return '/assets/image_1754034750863_wecd65.svg';
      case 'txt':
        return '/assets/image_1754034751401_4orsll.svg';
      default:
        return '/assets/image_1754034751973_4nurjc.svg';
    }
  };

  // 处理文件预览
  const handlePreview = (file: FileItem) => {
    message.info(`预览文件: ${file.name}`);
  };

  // 处理文件下载
  const handleDownload = async (file: FileItem) => {
    try {
      message.loading(`正在下载文件: ${file.name}`, 0);

      const response = await downloadFile({ fileName: file.name });

      if (response) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        message.destroy();
        message.success(`文件 ${file.name} 下载成功`);
      } else {
        message.destroy();
        message.error('下载失败，请重试');
      }
    } catch (error) {
      message.destroy();
      console.error('下载文件失败:', error);
      message.error('下载失败，请重试');
    }
  };

  // 处理单个文件选择
  const handleFileSelect = (fileName: string, checked: boolean) => {
    if (checked) {
      setSelectedFiles((prev) => [...prev, fileName]);
    } else {
      setSelectedFiles((prev) => prev.filter((name) => name !== fileName));
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(filteredFiles.map((file) => file.name));
    } else {
      setSelectedFiles([]);
    }
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedFiles.length === 0) {
      message.warning('请先选择要删除的文件');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedFiles.length} 个文件吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 1. 乐观更新：立即从本地列表移除文件
          const filesToDelete = [...selectedFiles];
          setFileList((prev) => prev.filter((file) => !filesToDelete.includes(file.name)));
          setSelectedFiles([]);

          message.loading(`正在删除 ${filesToDelete.length} 个文件...`, 0);

          // 2. 后台删除
          for (const fileName of filesToDelete) {
            await deleteAiRoleFile({
              id: assistantData.id,
              fileName: fileName,
            });
          }

          message.destroy();
          message.success(`成功删除 ${filesToDelete.length} 个文件`);

          // 3. 后台同步：静默刷新数据
          if (onRefresh) {
            onRefresh();
          }
        } catch (error) {
          message.destroy();
          console.error('批量删除失败:', error);
          message.error('批量删除失败，请重试');

          // 4. 失败回滚：重新刷新数据恢复状态
          if (onRefresh) {
            onRefresh();
          }
        }
      },
    });
  };

  // 处理文件删除
  const handleDelete = async (file: FileItem) => {
    if (!assistantData || !assistantData.id) {
      message.error('助手信息不完整，无法删除文件');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${file.name}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 1. 乐观更新：立即从本地列表移除文件
          setFileList((prev) => prev.filter((f) => f.id !== file.id));
          // 如果该文件被选中，也要从选中列表中移除
          setSelectedFiles((prev) => prev.filter((name) => name !== file.name));

          message.loading(`正在删除文件: ${file.name}`, 0);

          const res = await deleteAiRoleFile({
            id: assistantData.id,
            fileName: file.name,
          });

          if (res.code === 0) {
            message.destroy();
            message.success(`文件 ${file.name} 删除成功`);

            // 2. 后台同步：静默刷新数据
            if (onRefresh) {
              onRefresh();
            }
          } else {
            message.destroy();
            message.error(res.message || '删除失败');

            // 3. 失败回滚：重新刷新数据恢复状态
            if (onRefresh) {
              onRefresh();
            }
          }
        } catch (error) {
          message.destroy();
          console.error('删除文件失败:', error);
          message.error('删除失败，请重试');

          // 失败回滚：重新刷新数据恢复状态
          if (onRefresh) {
            onRefresh();
          }
        }
      },
    });
  };

  // 处理文件新增
  const handleAddFile = () => {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.docx';
    input.multiple = true;

    input.onchange = async (event: any) => {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      // 文件大小验证
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const isLt15M = file.size / 1024 / 1024 < 15;
        if (!isLt15M) {
          message.error(`文件 ${file.name} 大小不能超过15MB！`);
          return;
        }
      }

      try {
        if (!assistantData || !assistantData.id) {
          message.error('助手信息不完整，无法上传文件');
          return;
        }

        // 1. 乐观更新：立即添加文件到本地列表
        const newFiles: FileItem[] = Array.from(files as FileList).map((file: File, index: number) => {
          const extension = file.name.split('.').pop()?.toLowerCase();
          let type: 'pdf' | 'doc' | 'docx' | 'txt' = 'pdf';

          if (extension === 'doc' || extension === 'docx') {
            type = extension as 'doc' | 'docx';
          } else if (extension === 'txt') {
            type = 'txt';
          }

          return {
            id: `uploading-${Date.now()}-${index}`, // 临时ID
            name: file.name,
            type,
            size: `${(file.size / 1024 / 1024).toFixed(1)}MB`,
            uploadTime: new Date().toLocaleString(),
            uploading: true, // 标记为上传中
          };
        });

        // 立即更新文件列表
        setFileList((prev) => [...prev, ...newFiles]);
        message.loading('正在上传文件...', 0);

        // 2. 后台上传
        const requestObj = {
          id: assistantData.id,
          assistantName: assistantData.assistantName,
          type: assistantData.type,
          functionDes: assistantData.functionDes,
          requirement: assistantData.requirement,
          template: assistantData.template,
        };

        const formData = new FormData();
        Array.from(files as FileList).forEach((file: File) => {
          formData.append('files', file);
        });
        formData.append('request', JSON.stringify(requestObj));

        const res = await updateAiRole(formData);

        if (res.code === 0) {
          message.destroy();
          message.success(`成功上传 ${files.length} 个文件`);

          // 3. 后台同步：静默刷新数据
          if (onRefresh) {
            onRefresh();
          }
        } else {
          // 4. 失败回滚：移除临时文件
          setFileList((prev) => prev.filter((file) => !file.uploading));
          message.destroy();
          message.error(res.message || '上传失败');
        }
      } catch (error) {
        // 失败回滚：移除临时文件
        setFileList((prev) => prev.filter((file) => !file.uploading));
        message.destroy();
        console.error('上传文件失败:', error);
        message.error('上传失败，请重试');
      }
    };

    input.click();
  };

  // 过滤文件列表
  const filteredFiles = fileList.filter((file) => file.name.toLowerCase().includes(searchValue.toLowerCase()));

  return (
    <Modal title="知识库" open={visible} onCancel={onCancel} width={1000} footer={null} className="knowledge-base-modal common-modal">
      <div className="knowledge-base-content">
        {/* 头部标题 */}
        <div className="knowledge-base-header">
          <div className="file-count">文件列表({filteredFiles.length}个)</div>
        </div>

        {/* 操作区 */}
        <div className="actions-bar">
          <div className="left-section">
            <Checkbox
              checked={selectedFiles.length === filteredFiles.length && filteredFiles.length > 0}
              indeterminate={selectedFiles.length > 0 && selectedFiles.length < filteredFiles.length}
              onChange={(e) => {
                e.stopPropagation();
                handleSelectAll(e.target.checked);
              }}
            >
              全选
            </Checkbox>
            <Input
              placeholder="搜索文件名"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              prefix={<SearchOutlined />}
              className="search-input"
            />
          </div>
          <div className="action-buttons">
            <Button type="primary" onClick={handleAddFile}>
              新增
            </Button>
            <Button type="primary" danger onClick={handleBatchDelete} disabled={selectedFiles.length === 0}>
              批量删除 {selectedFiles.length > 0 && `(${selectedFiles.length})`}
            </Button>
          </div>
        </div>

        {/* 文件网格 */}
        <div className="file-grid">
          {filteredFiles.map((file) => (
            <div key={file.id} className={`file-card ${file.uploading ? 'uploading' : ''}`}>
              <div className="file-card-content">
                <div className={`file-checkbox ${selectedFiles.includes(file.name) ? 'checked' : ''}`}>
                  <Checkbox
                    checked={selectedFiles.includes(file.name)}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleFileSelect(file.name, e.target.checked);
                    }}
                  />
                </div>
                <div className="file-icon-wrapper">
                  <img src={getFileIcon(file.type)} alt={file.type} className="file-icon" />
                  <div className="file-actions">
                    <Popover
                      content={
                        <div className="file-action-menu">
                          <div className="action-item delete-item" onClick={() => handleDelete(file)}>
                            <DeleteOutlined />
                            <span>删除</span>
                          </div>
                        </div>
                      }
                      trigger="click"
                      placement="bottomLeft"
                    >
                      <img src="/assets/image_1754037517355_uqzeyi.svg" alt="更多" className="file-action-icon" />
                    </Popover>
                  </div>
                </div>
                <div className="file-info">
                  <Tooltip title={file.name}>
                    <div className="file-name">{file.name}</div>
                  </Tooltip>
                </div>
                <div className="file-card-actions">
                  <Button type="link" icon={<EyeOutlined />} onClick={() => handlePreview(file)} className="action-btn preview-btn">
                    预览
                  </Button>
                  <Button type="link" icon={<DownloadOutlined />} onClick={() => handleDownload(file)} className="action-btn download-btn">
                    下载
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 空状态 */}
        {filteredFiles.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">📁</div>
            <div className="empty-text">{searchValue ? '未找到匹配的文件' : '暂无文件'}</div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default KnowledgeBaseModal;
